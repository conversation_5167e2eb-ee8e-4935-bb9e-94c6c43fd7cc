import { handlePaymentSession } from "@/services/order";
import { redirect } from "next/navigation";
import { getPaymentService } from "@/services/payment";

export default async function ({
  params,
  searchParams,
}: {
  params: Promise<{ session_id: string }>;
  searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) {
  try {
    const { session_id } = await params;
    const urlParams = await searchParams;

    console.log('Payment success page accessed:', { session_id, urlParams });

    // 检查是否是PayPal返回
    if (urlParams.provider === 'paypal' || urlParams.PayerID) {
      // PayPal返回，session_id就是PayPal的订单ID
      console.log('Processing PayPal payment success, order ID:', session_id);

      const paymentService = getPaymentService();
      const session = await paymentService.getPaymentSession(session_id);

      console.log('PayPal session retrieved:', {
        id: session.id,
        payment_status: session.payment_status,
        order_no: session.metadata?.order_no,
        credits: session.metadata?.credits
      });

      await handlePaymentSession(session);
      console.log('PayPal payment session handled successfully');
    } else {
      // Stripe或其他支付方式，使用原有逻辑
      console.log('Processing non-PayPal payment success, session ID:', session_id);

      const paymentService = getPaymentService();
      const session = await paymentService.getPaymentSession(session_id);

      console.log('Payment session retrieved:', {
        id: session.id,
        payment_status: session.payment_status,
        order_no: session.metadata?.order_no
      });

      await handlePaymentSession(session);
      console.log('Payment session handled successfully');
    }
  } catch (e) {
    console.error("Payment success page error:", e);

    // 提供更详细的错误信息
    if (e instanceof Error) {
      console.error("Error details:", e.message, e.stack);
    }

    redirect(process.env.NEXT_PUBLIC_PAY_FAIL_URL || "/pricing?error=payment_failed");
  }

  // 成功处理后重定向到成功页面
  const successUrl = process.env.NEXT_PUBLIC_PAY_SUCCESS_URL || "/my-orders?payment=success";
  console.log('Redirecting to success URL:', successUrl);
  redirect(successUrl);
}
