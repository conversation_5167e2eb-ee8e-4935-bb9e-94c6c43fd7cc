import {
  IPaymentService,
  CreatePaymentSessionParams,
  PaymentSessionResponse,
  PaymentCallbackSession,
  PaymentProvider
} from '@/types/payment';
import { PaymentConfig } from './config';

/**
 * PayPal支付服务实现
 * 遵循SRP原则：只负责PayPal支付相关操作
 */
export class PayPalPaymentService implements IPaymentService {
  private config: PaymentConfig;
  private baseUrl: string;

  constructor() {
    this.config = PaymentConfig.getInstance();
    const paypalConfig = this.config.getPayPalConfig();
    // PayPal API基础URL
    this.baseUrl = paypalConfig.environment === 'live' 
      ? 'https://api-m.paypal.com' 
      : 'https://api-m.sandbox.paypal.com';
  }

  /**
   * 构建PayPal返回URL
   * 使用专门的PayPal返回处理端点
   */
  private buildReturnUrl(_successUrl: string, orderNo: string): string {
    // 获取基础URL
    const baseUrl = process.env.NEXT_PUBLIC_WEB_URL || 'http://localhost:3000';
    // 使用专门的PayPal返回处理路径，PayPal会添加token参数
    return `${baseUrl}/api/paypal-return?order_no=${orderNo}`;
  }

  /**
   * 获取PayPal访问令牌
   */
  private async getAccessToken(): Promise<string> {
    const paypalConfig = this.config.getPayPalConfig();
    const auth = Buffer.from(`${paypalConfig.clientId}:${paypalConfig.clientSecret}`).toString('base64');

    const response = await fetch(`${this.baseUrl}/v1/oauth2/token`, {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${auth}`,
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: 'grant_type=client_credentials',
    });

    if (!response.ok) {
      throw new Error('Failed to get PayPal access token');
    }

    const data = await response.json();
    return data.access_token;
  }

  /**
   * 创建PayPal支付会话
   */
  async createPaymentSession(params: CreatePaymentSessionParams): Promise<PaymentSessionResponse> {
    try {
      const accessToken = await this.getAccessToken();
      
      // PayPal金额需要转换为小数格式（Stripe使用分，PayPal使用元）
      const amount = (params.amount / 100).toFixed(2);
      
      let paypalOrder;

      if (params.is_subscription) {
        // 创建订阅产品和计划
        paypalOrder = await this.createSubscription(accessToken, params, amount);
      } else {
        // 创建一次性支付
        paypalOrder = await this.createOrder(accessToken, params, amount);
      }

      // 获取批准链接
      const approveLink = paypalOrder.links?.find((link: any) => link.rel === 'approve');
      
      if (!approveLink) {
        throw new Error('PayPal approve link not found');
      }

      return {
        session_id: paypalOrder.id,
        redirect_url: approveLink.href,
        order_no: params.order_no,
      };
    } catch (error) {
      console.error('PayPal payment session creation failed:', error);
      throw new Error(`Failed to create PayPal payment session: ${error}`);
    }
  }

  /**
   * 创建PayPal一次性支付订单
   */
  private async createOrder(accessToken: string, params: CreatePaymentSessionParams, amount: string) {
    const response = await fetch(`${this.baseUrl}/v2/checkout/orders`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`,
      },
      body: JSON.stringify({
        intent: 'CAPTURE',
        purchase_units: [{
          amount: {
            currency_code: params.currency.toUpperCase(),
            value: amount,
          },
          description: params.product_name,
          custom_id: params.order_no,
        }],
        application_context: {
          return_url: this.buildReturnUrl(params.success_url, params.order_no),
          cancel_url: params.cancel_url,
          brand_name: process.env.NEXT_PUBLIC_PROJECT_NAME || 'Your App',
          user_action: 'PAY_NOW',
          // 优化用户体验的设置
          shipping_preference: 'NO_SHIPPING', // 不需要收货地址
          landing_page: 'LOGIN', // 直接显示登录页面，而不是注册页面
          payment_method: {
            payee_preferred: 'IMMEDIATE_PAYMENT_REQUIRED' // 要求立即支付
          }
        },
        // 存储元数据
        metadata: {
          order_no: params.order_no,
          user_email: params.user_email,
          user_uuid: params.user_uuid,
          credits: params.credits.toString(),
          product_name: params.product_name,
        },
      }),
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`PayPal order creation failed: ${error}`);
    }

    return await response.json();
  }

  /**
   * 创建PayPal订阅
   */
  private async createSubscription(accessToken: string, params: CreatePaymentSessionParams, amount: string) {
    try {
      // 1. 创建产品
      const product = await this.createProduct(accessToken, params);

      // 2. 创建订阅计划
      const plan = await this.createSubscriptionPlan(accessToken, product.id, params, amount);

      // 3. 创建订阅
      const subscription = await this.createSubscriptionOrder(accessToken, plan.id, params);

      return subscription;
    } catch (error) {
      console.error('PayPal subscription creation failed:', error);
      throw error;
    }
  }

  /**
   * 创建PayPal产品
   */
  private async createProduct(accessToken: string, params: CreatePaymentSessionParams) {
    const productData = {
      name: params.product_name,
      description: `订阅计划 - ${params.product_name}`,
      type: 'SERVICE',
      category: 'SOFTWARE'
    };

    const response = await fetch(`${this.baseUrl}/v1/catalogs/products`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`,
        'PayPal-Request-Id': `product-${params.order_no}`,
      },
      body: JSON.stringify(productData),
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`PayPal product creation failed: ${response.status} ${errorData}`);
    }

    return await response.json();
  }

  /**
   * 创建PayPal订阅计划
   */
  private async createSubscriptionPlan(accessToken: string, productId: string, params: CreatePaymentSessionParams, amount: string) {
    // 确定计费周期
    const intervalUnit = params.interval === 'monthly' ? 'MONTH' : 'YEAR';
    const intervalCount = 1;

    const planData = {
      product_id: productId,
      name: `${params.product_name} - ${params.interval}`,
      description: `订阅计划 - ${params.product_name}`,
      status: 'ACTIVE',
      billing_cycles: [
        {
          frequency: {
            interval_unit: intervalUnit,
            interval_count: intervalCount,
          },
          tenure_type: 'REGULAR',
          sequence: 1,
          total_cycles: 0, // 0 表示无限循环
          pricing_scheme: {
            fixed_price: {
              value: amount,
              currency_code: params.currency.toUpperCase(),
            },
          },
        },
      ],
      payment_preferences: {
        auto_bill_outstanding: true,
        setup_fee_failure_action: 'CONTINUE',
        payment_failure_threshold: 3,
      },
    };

    const response = await fetch(`${this.baseUrl}/v1/billing/plans`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`,
        'PayPal-Request-Id': `plan-${params.order_no}`,
      },
      body: JSON.stringify(planData),
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`PayPal plan creation failed: ${response.status} ${errorData}`);
    }

    return await response.json();
  }

  /**
   * 创建PayPal订阅订单
   */
  private async createSubscriptionOrder(accessToken: string, planId: string, params: CreatePaymentSessionParams) {
    const subscriptionData = {
      plan_id: planId,
      start_time: new Date(Date.now() + 60000).toISOString(), // 1分钟后开始
      subscriber: {
        email_address: params.user_email,
      },
      custom_id: params.order_no, // 添加订单号以便后续识别
      application_context: {
        brand_name: 'Watermark Remover',
        locale: 'en-US',
        shipping_preference: 'NO_SHIPPING',
        user_action: 'SUBSCRIBE_NOW',
        payment_method: {
          payer_selected: 'PAYPAL',
          payee_preferred: 'IMMEDIATE_PAYMENT_REQUIRED',
        },
        return_url: params.success_url.replace('{CHECKOUT_SESSION_ID}', 'subscription_success'),
        cancel_url: params.cancel_url,
      },
    };

    const response = await fetch(`${this.baseUrl}/v1/billing/subscriptions`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`,
        'PayPal-Request-Id': `subscription-${params.order_no}`,
      },
      body: JSON.stringify(subscriptionData),
    });

    if (!response.ok) {
      const errorData = await response.text();
      throw new Error(`PayPal subscription creation failed: ${response.status} ${errorData}`);
    }

    return await response.json();
  }

  /**
   * 捕获PayPal支付
   */
  private async capturePayment(orderId: string): Promise<any> {
    const accessToken = await this.getAccessToken();

    const response = await fetch(`${this.baseUrl}/v2/checkout/orders/${orderId}/capture`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${accessToken}`,
      },
    });

    if (!response.ok) {
      const error = await response.text();
      throw new Error(`PayPal capture failed: ${error}`);
    }

    return await response.json();
  }

  /**
   * 获取PayPal支付会话详情
   * 如果订单还未捕获，会自动捕获支付
   */
  async getPaymentSession(session_id: string): Promise<PaymentCallbackSession> {
    try {
      const accessToken = await this.getAccessToken();

      // 首先尝试作为订单获取
      let response = await fetch(`${this.baseUrl}/v2/checkout/orders/${session_id}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${accessToken}`,
        },
      });

      let order;
      let isSubscription = false;

      if (!response.ok) {
        // 如果订单获取失败，尝试作为订阅获取
        response = await fetch(`${this.baseUrl}/v1/billing/subscriptions/${session_id}`, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${accessToken}`,
          },
        });

        if (!response.ok) {
          throw new Error('Failed to retrieve PayPal order or subscription');
        }

        order = await response.json();
        isSubscription = true;
      } else {
        order = await response.json();
      }

      let isCompleted;
      let customId;

      if (isSubscription) {
        // 处理订阅
        isCompleted = order.status === 'ACTIVE';
        customId = order.custom_id;
      } else {
        // 处理一次性支付
        // 如果订单已批准但未捕获，则捕获支付
        if (order.status === 'APPROVED') {
          console.log('PayPal order approved, capturing payment...');
          order = await this.capturePayment(session_id);
        }

        isCompleted = order.status === 'COMPLETED';

        // 从不同位置尝试获取custom_id（我们的订单号）
        customId = order.purchase_units?.[0]?.custom_id;

        // 如果是捕获后的响应，custom_id在captures中
        if (!customId && order.purchase_units?.[0]?.payments?.captures?.[0]) {
          customId = order.purchase_units[0].payments.captures[0].custom_id;
        }
      }

      // 尝试从数据库获取订单信息以补充metadata
      let orderMetadata = {};
      if (customId) {
        try {
          // 这里我们需要从数据库获取订单信息来补充metadata
          // 因为PayPal不像Stripe那样直接存储自定义metadata
          const { findOrderByOrderNo } = await import('@/models/order');
          const dbOrder = await findOrderByOrderNo(customId);
          if (dbOrder) {
            orderMetadata = {
              order_no: customId,
              user_email: dbOrder.user_email,
              user_uuid: dbOrder.user_uuid,
              credits: dbOrder.credits?.toString(),
              product_name: dbOrder.product_name || `订单 ${customId}`,
              amount: dbOrder.amount,
              currency: dbOrder.currency,
            };
          }
        } catch (error) {
          console.warn('Failed to fetch order metadata from database:', error);
          // 如果数据库查询失败，至少保留order_no
          orderMetadata = { order_no: customId };
        }
      }

      // 构建返回对象
      let customerEmail, amountTotal, currency;

      if (isSubscription) {
        customerEmail = order.subscriber?.email_address;
        // 对于订阅，从数据库获取金额信息
        if (orderMetadata && orderMetadata.amount) {
          amountTotal = orderMetadata.amount;
        }
        if (orderMetadata && orderMetadata.currency) {
          currency = orderMetadata.currency;
        }
      } else {
        customerEmail = order.payer?.email_address;
        amountTotal = order.purchase_units?.[0]?.amount?.value ?
          Math.round(parseFloat(order.purchase_units[0].amount.value) * 100) : undefined;
        currency = order.purchase_units?.[0]?.amount?.currency_code?.toLowerCase();
      }

      return {
        id: order.id,
        payment_status: isCompleted ? 'paid' : 'unpaid',
        customer_email: customerEmail,
        metadata: orderMetadata,
        amount_total: amountTotal,
        currency: currency,
      };
    } catch (error) {
      console.error('Failed to retrieve PayPal session:', error);
      throw new Error(`Failed to retrieve PayPal session: ${error}`);
    }
  }

  /**
   * 验证PayPal webhook签名
   */
  verifyWebhookSignature(payload: string, _signature: string, _secret: string): any {
    // PayPal webhook验证逻辑
    // 这里需要实现PayPal的webhook验证
    // 暂时返回解析的payload
    try {
      return JSON.parse(payload);
    } catch (error) {
      throw new Error(`PayPal webhook payload parsing failed: ${error}`);
    }
  }

  /**
   * 获取支付服务提供商名称
   */
  getProviderName(): PaymentProvider {
    return PaymentProvider.PAYPAL;
  }
}
