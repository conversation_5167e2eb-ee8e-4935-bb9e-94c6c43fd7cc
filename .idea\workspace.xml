<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="e72dc67b-811c-4faf-ab72-f6a488a1b0da" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/.env.development" beforeDir="false" afterPath="$PROJECT_DIR$/.env.development" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="mavenHome" value="D:\soft\apache-maven-3.6.1" />
        <option name="userSettingsFile" value="D:\soft\apache-maven-3.6.1\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="2zqyZBVQfOBKXHNWvfGAOFQwgrS" />
  <component name="ProjectViewState">
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "git-widget-placeholder": "master",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "D:/实习/而立/shipany_code/shipany-template-one",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "pnpm",
    "settings.editor.selected.configurable": "reference.projectsettings.compiler.javacompiler",
    "ts.external.directory.path": "D:\\实习\\而立\\shipany_code\\shipany-template-one\\node_modules\\typescript\\lib",
    "vue.rearranger.settings.migration": "true"
  }
}]]></component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="e72dc67b-811c-4faf-ab72-f6a488a1b0da" name="Changes" comment="" />
      <created>1752472950294</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1752472950294</updated>
      <workItem from="1752472951351" duration="546000" />
      <workItem from="1752474201706" duration="631000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>