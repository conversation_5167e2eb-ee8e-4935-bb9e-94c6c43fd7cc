import {
  findCreditByOrderNo,
  getUserValidCredits,
  insertCredit,
} from "@/models/credit";
import { credits as creditsTable } from "@/db/schema";
import { getIsoTimestr, getOneYearLaterTimestr } from "@/lib/time";
import { getSnowId } from "@/lib/hash";
import { Order } from "@/types/order";
import { UserCredits } from "@/types/user";
import { getFirstPaidOrderByUserUuid } from "@/models/order";

export enum CreditsTransType {
  NewUser = "new_user", // initial credits for new user
  OrderPay = "order_pay", // user pay for credits
  SystemAdd = "system_add", // system add credits
  MonthlyGift = "monthly_gift", // monthly gift for subscription users
  Ping = "ping", // cost for ping api
  RemoveWatermark = "remove_watermark", // cost for remove watermark api
}

export enum CreditsAmount {
  NewUserGet = 10,
  MonthlyGift = 3, // 订阅用户每月赠送3积分
  PingCost = 1,
  RemoveWatermarkCost = 1, // 去水印消耗1积分
}

export async function getUserCredits(user_uuid: string): Promise<UserCredits> {
  let user_credits: UserCredits = {
    left_credits: 0,
  };

  try {
    const first_paid_order = await getFirstPaidOrderByUserUuid(user_uuid);
    if (first_paid_order) {
      user_credits.is_recharged = true;
    }

    const credits = await getUserValidCredits(user_uuid);
    if (credits) {
      credits.forEach((v) => {
        user_credits.left_credits += v.credits || 0;
      });
    }

    if (user_credits.left_credits < 0) {
      user_credits.left_credits = 0;
    }

    // 检查是否需要赠送月度积分（异步执行，不阻塞主流程）
    if (user_credits.left_credits === 0) {
      checkAndGiveMonthlyGiftForUser(user_uuid).catch(error => {
        console.error('Failed to check monthly gift:', error);
      });
    }

    if (user_credits.left_credits > 0) {
      user_credits.is_pro = true;
    }

    return user_credits;
  } catch (e) {
    console.log("get user credits failed: ", e);
    return user_credits;
  }
}

export async function decreaseCredits({
  user_uuid,
  trans_type,
  credits,
}: {
  user_uuid: string;
  trans_type: CreditsTransType;
  credits: number;
}) {
  try {
    let order_no = "";
    let expired_at = "";
    let left_credits = 0;

    const userCredits = await getUserValidCredits(user_uuid);
    if (userCredits) {
      for (let i = 0, l = userCredits.length; i < l; i++) {
        const credit = userCredits[i];
        left_credits += credit.credits;

        // credit enough for cost
        if (left_credits >= credits) {
          order_no = credit.order_no || "";
          expired_at = credit.expired_at?.toISOString() || "";
          break;
        }

        // look for next credit
      }
    }

    const new_credit: typeof creditsTable.$inferInsert = {
      trans_no: getSnowId(),
      created_at: new Date(getIsoTimestr()),
      expired_at: new Date(expired_at),
      user_uuid: user_uuid,
      trans_type: trans_type,
      credits: 0 - credits,
      order_no: order_no,
    };
    await insertCredit(new_credit);
  } catch (e) {
    console.log("decrease credits failed: ", e);
    throw e;
  }
}

export async function increaseCredits({
  user_uuid,
  trans_type,
  credits,
  expired_at,
  order_no,
}: {
  user_uuid: string;
  trans_type: string;
  credits: number;
  expired_at?: string;
  order_no?: string;
}) {
  try {
    const new_credit: typeof creditsTable.$inferInsert = {
      trans_no: getSnowId(),
      created_at: new Date(getIsoTimestr()),
      user_uuid: user_uuid,
      trans_type: trans_type,
      credits: credits,
      order_no: order_no || "",
      expired_at: expired_at ? new Date(expired_at) : null,
    };
    await insertCredit(new_credit);
  } catch (e) {
    console.log("increase credits failed: ", e);
    throw e;
  }
}

export async function updateCreditForOrder(order: Order) {
  try {
    const credit = await findCreditByOrderNo(order.order_no);
    if (credit) {
      // order already increased credit
      return;
    }

    await increaseCredits({
      user_uuid: order.user_uuid,
      trans_type: CreditsTransType.OrderPay,
      credits: order.credits,
      expired_at: order.expired_at,
      order_no: order.order_no,
    });
  } catch (e) {
    console.log("update credit for order failed: ", e);
    throw e;
  }
}

/**
 * 检查用户是否已经在当月获得了赠送积分
 */
export async function hasMonthlyGiftThisMonth(user_uuid: string): Promise<boolean> {
  try {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);

    const { db } = await import("@/db");
    const { credits } = await import("@/db/schema");
    const { and, eq, gte, lte } = await import("drizzle-orm");

    const [credit] = await db()
      .select()
      .from(credits)
      .where(
        and(
          eq(credits.user_uuid, user_uuid),
          eq(credits.trans_type, CreditsTransType.MonthlyGift),
          gte(credits.created_at, startOfMonth),
          lte(credits.created_at, endOfMonth)
        )
      )
      .limit(1);

    return !!credit;
  } catch (e) {
    console.log("check monthly gift failed: ", e);
    return false;
  }
}

/**
 * 为订阅用户赠送月度积分（仅当用户积分为0时）
 */
export async function giveMonthlyGiftToSubscriptionUsers() {
  try {
    const { getAllActiveSubscriptionUsers } = await import("@/models/order");
    const activeSubscriptionUsers = await getAllActiveSubscriptionUsers();

    console.log(`Found ${activeSubscriptionUsers.length} active subscription users`);

    for (const user_uuid of activeSubscriptionUsers) {
      try {
        // 检查用户当前积分
        const userCredits = await getUserCredits(user_uuid);

        // 只有当用户积分为0时才赠送
        if (userCredits.left_credits > 0) {
          console.log(`User ${user_uuid} has ${userCredits.left_credits} credits, skipping monthly gift`);
          continue;
        }

        // 检查是否已经在当月获得了赠送
        const hasGift = await hasMonthlyGiftThisMonth(user_uuid);
        if (hasGift) {
          console.log(`User ${user_uuid} already received monthly gift this month`);
          continue;
        }

        // 赠送月度积分
        await increaseCredits({
          user_uuid,
          trans_type: CreditsTransType.MonthlyGift,
          credits: CreditsAmount.MonthlyGift,
          expired_at: getOneYearLaterTimestr(), // 一年后过期
        });

        console.log(`Monthly gift of ${CreditsAmount.MonthlyGift} credits given to user ${user_uuid}`);
      } catch (error) {
        console.error(`Failed to give monthly gift to user ${user_uuid}:`, error);
      }
    }
  } catch (e) {
    console.log("give monthly gift to subscription users failed: ", e);
    throw e;
  }
}

/**
 * 检查单个用户是否需要月度积分赠送
 */
export async function checkAndGiveMonthlyGiftForUser(user_uuid: string) {
  try {
    const { getActiveSubscriptionsByUserUuid } = await import("@/models/order");
    const activeSubscriptions = await getActiveSubscriptionsByUserUuid(user_uuid);

    // 如果用户没有有效订阅，跳过
    if (!activeSubscriptions || activeSubscriptions.length === 0) {
      return;
    }

    // 检查用户当前积分
    const userCredits = await getUserCredits(user_uuid);

    // 只有当用户积分为0时才赠送
    if (userCredits.left_credits > 0) {
      return;
    }

    // 检查是否已经在当月获得了赠送
    const hasGift = await hasMonthlyGiftThisMonth(user_uuid);
    if (hasGift) {
      return;
    }

    // 赠送月度积分
    await increaseCredits({
      user_uuid,
      trans_type: CreditsTransType.MonthlyGift,
      credits: CreditsAmount.MonthlyGift,
      expired_at: getOneYearLaterTimestr(), // 一年后过期
    });

    console.log(`Monthly gift of ${CreditsAmount.MonthlyGift} credits given to user ${user_uuid}`);
  } catch (error) {
    console.error(`Failed to check and give monthly gift to user ${user_uuid}:`, error);
  }
}
